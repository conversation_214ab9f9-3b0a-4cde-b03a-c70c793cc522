import 'package:Kairos/core/widgets/common/hero_widget.dart';
import 'package:Kairos/core/widgets/common/snackbar_widget.dart';
import 'package:Kairos/features/authentication/data/datasources/auth_local_datasource.dart';
import 'package:Kairos/features/schools/domain/entities/etablissement_utilisateur.dart';
import 'package:Kairos/features/schools/presentation/bloc/schools_cubit.dart';
import 'package:Kairos/features/schools/presentation/bloc/schools_state.dart'; // Import SchoolsState
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:flutter_bloc/flutter_bloc.dart'; // Import BlocConsumer
import 'package:Kairos/features/schools/presentation/pages/liste_etablissements/liste_etablissement_widgets/list_etablisssement_utilisateur.widget.dart';
import 'package:Kairos/features/schools/presentation/pages/liste_etablissements/liste_etablissement_widgets/liste_empty_alert.widget.dart';
import 'package:Kairos/core/widgets/indicators/custom_spinner.dart';
import 'package:Kairos/core/di/injection_container.dart'; // Import sl

class ListeEtablissement extends StatefulWidget{
  const ListeEtablissement({super.key});

  @override
  State<ListeEtablissement> createState() => _ListeEtablissementState();
}

class _ListeEtablissementState extends State<ListeEtablissement>{
  // Inject SchoolsLocalDataSource
  final AuthLocalDataSource _authLocalDataSource = sl<AuthLocalDataSource>();
  final List<EtablissementUtilisateur> _userSchools = [];

  @override
  void initState() {
    super.initState();
    // Fetch user schools when the page initializes
    _fetchUserSchools();
  }

  Future<void> _fetchUserSchools() async {
    final String? phoneNumber = await _authLocalDataSource.getPhoneNumber();
    if (phoneNumber != null) {
      _userSchools.addAll(await context.read<SchoolsCubit>().getUserSchools(phoneNumber));
    } else {
      // Handle case where phone number is not available
      debugPrint('Phone number not available.');
      // Optionally dispatch an error state to the cubit
    }
  }

  @override
  Widget build(BuildContext context){
    return Scaffold(
      body: BlocConsumer<SchoolsCubit, SchoolsState>(
        listener: (context, state) {
          // TODO: Implement listener logic based on SchoolsState changes
          // For example, show a Snackbar on error or navigate on success
          if (state is SchoolsError) {
            // Show error message
            debugPrint('Error: ${state.message}');
            CustomSnackbar(message: state.message).getSnackBar();
          } else if(state is SchoolsLoaded){
            debugPrint('Schools loaded: ${state.data.length}');
          }
        },
        builder: (context, state) {
          // Build UI based on the current SchoolsState
          return Column(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              HeroWidget(),
              SizedBox(height: 20),
              Text(state is SchoolsLoaded && state.data.isEmpty? "ACTIVATION D'UN NOUVEL ÉTABLISSEMENT": "ÉTABLISSEMENTS LIÉS AU PROFIL", textAlign: TextAlign.center, style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
                      Divider(color: Theme.of(context).primaryColor, thickness: 4, height: 20, indent: 150, endIndent: 150,),
              SizedBox(height: 2),
              if(state is SchoolsLoaded && state.data.isNotEmpty)
                Text("Veuillez choisir un établissement pour vous y connecter", textAlign: TextAlign.center, style: TextStyle(fontSize: 12, color: Colors.grey.shade800),),
              if (state is SchoolsLoading)
                // Display loading indicator
                Flexible(
                  flex: 12,
                  child: Center(
                    child: CustomSpinner(
                      size: 60.0,
                      strokeWidth: 5.0,
                    ),
                  ),
                )
              else if (state is SchoolsLoaded)
                // Display the list of schools
                if (state.data.isNotEmpty) // Use state.data
                  Flexible(
                    flex: 6,
                    child: Center(
                      child: Column(
                        children: [
                          ListeEtablissementUtilisateurWidget(
                            userSchools: state.data.isEmpty? _userSchools: state.data, // Use state.data
                          ),
                        ],
                      ),
                    ),
                  )
                else
                  // Display empty state message
                  Flexible(
                    flex: 6,
                    child: ListeEmptyAlert(),
                  )
              else if (state is SchoolsError)
                // Display error message (optional, can be handled by listener)
                Flexible(
                  flex: 6,
                  child: Center(
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          SvgPicture.asset("assets/icons/icon_aucun_etablissement.svg"),
                          Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Center(
                              child: Text(
                                "Erreur: ${state.message.contains('Champ requis manquant') && state.message.contains('token') ? 'Votre session a expiré, veuillez vous reconnecter.' : state.message}",
                                textAlign: TextAlign.center,
                                ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                )
              else
                // Initial state or other states
                Container(), // Or a default widget
              SizedBox(height: 20),
              if(state is! SchoolsError) FilledButton(
                style: ButtonStyle(
                  backgroundColor: WidgetStateProperty.all(Theme.of(context).primaryColor),
                  fixedSize: WidgetStateProperty.all(Size(300, 50))
                ),
                onPressed: () async {
                  debugPrint('the user clicked on `Continue` button');
                  final userSchools = await Navigator.pushNamed(context, "/activate_school");
                  debugPrint('userSchools: $userSchools');
                  if(userSchools != null && (userSchools as List<EtablissementUtilisateur>).isNotEmpty){
                    await _fetchUserSchools();
                  }
                },
                child: Text(state is SchoolsLoaded && state.data.isEmpty? "ACTIVER UN ÉTABLISSEMENT": "CRÉER UN NOUVEL ÉTABLISSEMENT", style: TextStyle(fontWeight: FontWeight.bold), ),
              ) else if((state.message.contains('Champ requis manquant') && state.message.contains('token')) || 
              (state.message.contains('token') && state.message.contains("expiré")))
                
                        FilledButton(
                          style: ButtonStyle(
                            backgroundColor: WidgetStateProperty.all(Theme.of(context).primaryColor),
                            minimumSize: WidgetStateProperty.all(const Size(150, 50)),
                          ),
                          onPressed: () async {
                            await Navigator.pushNamed(
                              context,
                              "/accueil",
                              arguments: {'goToPhoneAuth': true},
                            );
                          },
                          child: Text("SE RECONNECTER")),
              SizedBox(height: 20),
              SvgPicture.asset("assets/images/logo_footer.svg"),
              SizedBox(height: 10)
            ],
          );
        },
      ),
    );
  }
}