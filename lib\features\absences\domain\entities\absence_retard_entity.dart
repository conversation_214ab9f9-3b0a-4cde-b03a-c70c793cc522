import 'package:equatable/equatable.dart';

/// Entity representing both absence and tardiness records
class AbsenceRetardEntity extends Equatable {
  final String type; // "A" for Absence, "R" for Retard (delay)
  final int? nombreMinuteRetard; // Number of minutes late (only for tardiness)
  final String dateEnregistrement; // Date when the absence/tardiness was recorded
  final bool justifie; // Whether the absence/tardiness is justified
  final String? dateJustification; // Date when justification was provided
  final String cours; // Course name
  final String professeur; // Teacher name
  final String classe; // Class name

  const AbsenceRetardEntity({
    required this.type,
    this.nombreMinuteRetard,
    required this.dateEnregistrement,
    required this.justifie,
    this.dateJustification,
    required this.cours,
    required this.professeur,
    required this.classe,
  });

  @override
  List<Object?> get props => [
        type,
        nombreMinuteRetard,
        dateEnregistrement,
        justifie,
        dateJustification,
        cours,
        professeur,
        classe,
      ];

  /// Check if this is an absence record
  bool get isAbsence => type == 'A';

  /// Check if this is a tardiness record
  bool get isTardiness => type == 'R';

  /// Get formatted duration for tardiness records
  String? get formattedDuration {
    if (isTardiness && nombreMinuteRetard != null) {
      return '${nombreMinuteRetard}mn';
    }
    return null;
  }
}
