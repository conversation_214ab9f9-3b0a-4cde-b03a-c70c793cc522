import '../../domain/entities/absence_retard_entity.dart';

/// Model class for absence and tardiness records from API
class AbsenceRetardModel extends AbsenceRetardEntity {
  const AbsenceRetardModel({
    required super.type,
    super.nombreMinuteRetard,
    required super.dateEnregistrement,
    required super.justifie,
    super.dateJustification,
    required super.cours,
    required super.professeur,
    required super.classe,
  });

  /// Create model from JSON
  factory AbsenceRetardModel.fromJson(Map<String, dynamic> json) {
    return AbsenceRetardModel(
      type: json['type'] as String,
      nombreMinuteRetard: json['nombreMinuteRetard'] as int?,
      dateEnregistrement: json['dateEnregistrement'] as String,
      justifie: json['justifie'] as bool? ?? false,
      dateJustification: json['dateJustification'] as String?,
      cours: json['cours'] as String,
      professeur: json['professeur'] as String,
      classe: json['classe'] as String,
    );
  }

  /// Convert model to JSON
  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'nombreMinuteRetard': nombreMinuteRetard,
      'dateEnregistrement': dateEnregistrement,
      'justifie': justifie,
      'dateJustification': dateJustification,
      'cours': cours,
      'professeur': professeur,
      'classe': classe,
    };
  }

  /// Convert model to entity
  AbsenceRetardEntity toEntity() {
    return AbsenceRetardEntity(
      type: type,
      nombreMinuteRetard: nombreMinuteRetard,
      dateEnregistrement: dateEnregistrement,
      justifie: justifie,
      dateJustification: dateJustification,
      cours: cours,
      professeur: professeur,
      classe: classe,
    );
  }

  /// Create model from entity
  factory AbsenceRetardModel.fromEntity(AbsenceRetardEntity entity) {
    return AbsenceRetardModel(
      type: entity.type,
      nombreMinuteRetard: entity.nombreMinuteRetard,
      dateEnregistrement: entity.dateEnregistrement,
      justifie: entity.justifie,
      dateJustification: entity.dateJustification,
      cours: entity.cours,
      professeur: entity.professeur,
      classe: entity.classe,
    );
  }
}
