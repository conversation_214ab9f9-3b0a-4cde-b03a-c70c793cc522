import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart'; // Import material for debugPrint

import '../../../../core/api/api_client.dart';
import '../../../../core/api/api_endpoints.dart';
import '../models/absence_retard_model.dart';

/// Abstract interface for absences retards remote data source
abstract class AbsencesRetardsRemoteDataSource {
  /// Get absences and tardiness records from API
  Future<List<AbsenceRetardModel>> getAbsencesRetards({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  });

  /// Get filtered absences and tardiness records from API
  Future<List<AbsenceRetardModel>> getAbsencesRetardsFiltres({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
    String? startDate,
    String? endDate,
  });
}

/// Implementation of AbsencesRetardsRemoteDataSource
class AbsencesRetardsRemoteDataSourceImpl implements AbsencesRetardsRemoteDataSource {
  final ApiClient apiClient;

  AbsencesRetardsRemoteDataSourceImpl({required this.apiClient});

  @override
  Future<List<AbsenceRetardModel>> getAbsencesRetards({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  }) async {
    try {
      // Build query parameters
      final Map<String, dynamic> queryParameters = {
        'codeEtab': codeEtab,
        'telephone': telephone.replaceAll("+", ""),
        'codeEtudiant': codeEtudiant,
      };

      // Add codeUtilisateur for PAR profile if provided
      if (codeUtilisateur != null && codeUtilisateur.isNotEmpty) {
        queryParameters['codeUtilisateur'] = codeUtilisateur;
      }

      // Make HTTP GET request to the absencesRetards endpoint
      final response = await apiClient.getWithToken(
        ApiEndpoints.absencesRetards,
        queryParameters: queryParameters,
        options: Options(responseType: ResponseType.bytes)
      );

      final decodedResponse = latin1.decode(response.data);
      final jsonResponse = jsonDecode(decodedResponse);

      // Debug print the raw response data
      debugPrint('AbsencesRetardsRemoteDataSourceImpl: getAbsencesRetards raw response data: $jsonResponse');

      // Parse response data
      if (jsonResponse is List) {
        return jsonResponse
            .map((json) => AbsenceRetardModel.fromJson(json as Map<String, dynamic>))
            .toList();
      } else {
        throw Exception('Invalid response format: expected List but got ${response.data.runtimeType}');
      }
    } catch (e) {
      // Re-throw the exception after logging
      debugPrint('AbsencesRetardsRemoteDataSourceImpl: Failed to get absences retards: $e');
      rethrow;
    }
  }

  @override
  Future<List<AbsenceRetardModel>> getAbsencesRetardsFiltres({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
    String? startDate,
    String? endDate,
  }) async {
    try {
      // Build query parameters
      final Map<String, dynamic> queryParameters = {
        'codeEtab': codeEtab,
        'telephone': telephone.replaceAll("+", ""),
        'codeEtudiant': codeEtudiant,
      };

      // Add codeUtilisateur for PAR profile if provided
      if (codeUtilisateur != null && codeUtilisateur.isNotEmpty) {
        queryParameters['codeUtilisateur'] = codeUtilisateur;
      }

      // Add date filters if provided
      if (startDate != null && startDate.isNotEmpty) {
        queryParameters['dateDebut'] = startDate;
      }
      if (endDate != null && endDate.isNotEmpty) {
        queryParameters['dateFin'] = endDate;
      }

      // Make HTTP GET request to the absencesRetardsFiltres endpoint
      final response = await apiClient.getWithToken(
        ApiEndpoints.absencesRetardsFiltres,
        queryParameters: queryParameters,
        options: Options(responseType: ResponseType.bytes)
      );

      final decodedResponse = latin1.decode(response.data);
      final jsonResponse = jsonDecode(decodedResponse);

      // Debug print the raw response data
      debugPrint('AbsencesRetardsRemoteDataSourceImpl: getAbsencesRetardsFiltres raw response data: $jsonResponse');

      // Parse response data
      if (jsonResponse is List) {
        return jsonResponse
            .map((json) => AbsenceRetardModel.fromJson(json as Map<String, dynamic>))
            .toList();
      } else {
        throw Exception('Invalid response format: expected List but got ${response.data.runtimeType}');
      }
    } catch (e) {
      // Re-throw the exception after logging
      debugPrint('AbsencesRetardsRemoteDataSourceImpl: Failed to get filtered absences retards: $e');
      rethrow;
    }
  }
}
