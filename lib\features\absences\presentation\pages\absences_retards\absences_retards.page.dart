import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:Kairos/core/enums/header_enums.dart';
import 'package:Kairos/core/widgets/layout/custom_app_bar.widget.dart';
import 'package:Kairos/core/widgets/indicators/custom_spinner.dart';
import 'package:Kairos/core/widgets/inputs/search_bar_sliver.widget.dart';
import 'package:Kairos/core/widgets/common/empty_message.widget.dart';
import 'package:Kairos/features/absences/presentation/pages/absences_retards/absence_item.widget.dart';
import 'package:Kairos/features/absences/presentation/bloc/absences_retards_cubit.dart';
import 'package:Kairos/features/absences/presentation/bloc/absences_retards_state.dart';
import 'package:Kairos/features/absences/domain/entities/absence_retard_entity.dart';
import 'package:Kairos/core/utils/date_utils.dart' as date_utils;
import 'package:Kairos/features/authentication/data/datasources/auth_local_datasource.dart';
import 'package:Kairos/features/schools/domain/entities/etablissement_utilisateur.dart';
import 'package:Kairos/features/student_records/domain/entities/enfant_tuteur_entity.dart';
import 'package:Kairos/core/di/injection_container.dart';

class AbsencesRetardsPage extends StatefulWidget {
  final EtablissementUtilisateur? school;
  final EnfantTuteurEntity? etudiant;

  const AbsencesRetardsPage({
    super.key,
    this.school,
    this.etudiant,
  });

  @override
  State<AbsencesRetardsPage> createState() => _AbsencesRetardsPageState();
}

class _AbsencesRetardsPageState extends State<AbsencesRetardsPage> with SingleTickerProviderStateMixin {
  bool _isSearchBarVisible = false;
  late TextEditingController _searchController;
  List<AbsenceRetardEntity> _filteredAbsencesRetards = [];
  List<AbsenceRetardEntity> _allAbsencesRetards = [];

  // Date filter state
  String? _startDateFilter;
  String? _endDateFilter;

  // Animation controller
  late AnimationController _searchAnimationController;

  // Data sources
  final AuthLocalDataSource _authLocalDataSource = sl<AuthLocalDataSource>();

  // Navigation arguments
  EtablissementUtilisateur? _school;
  EnfantTuteurEntity? _etudiant;
  




  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController();
    _searchAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    // Add listener to filter absences retards when search text changes
    _searchController.addListener(_filterAbsencesRetards);

    // Load navigation arguments and initialize data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadArgumentsAndData();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchAnimationController.dispose();
    super.dispose();
  }

  /// Load navigation arguments and initialize data
  void _loadArgumentsAndData() {
    final args = ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;

    if (args != null) {
      _school = args['school'] as EtablissementUtilisateur?;
      _etudiant = args['etudiant'] as EnfantTuteurEntity?;
    } else {
      // Use constructor parameters if no route arguments
      _school = widget.school;
      _etudiant = widget.etudiant;
    }

    // Load absences retards data
    _loadAbsencesRetardsData();
  }

  /// Load absences retards data using BLoC
  Future<void> _loadAbsencesRetardsData() async {
    if (_school == null) return;

    try {
      final phoneNumber = await _authLocalDataSource.getPhoneNumber();
      if (phoneNumber != null && mounted) {
        // Determine parameters based on user profile
        final codeEtudiant = _etudiant?.codeEtudiant ?? _school!.codeUtilisateur;
        final codeUtilisateur = _etudiant != null ? _school!.codeUtilisateur : null;
       
        context.read<AbsencesRetardsCubit>().loadAbsencesRetards(
          codeEtab: _school!.codeEtab,
          telephone: phoneNumber,
          codeEtudiant: codeEtudiant,
          codeUtilisateur: codeUtilisateur,
        );
      
    }
    } catch (e) {
      debugPrint('Error loading absences retards data: $e');
    }
  }
  /// Load absences retards data using BLoC
  Future<void> _loadAbsencesRetardsDataWithDateFilter() async {
    if (_school == null) return;
    if(_startDateFilter != null && _endDateFilter != null) {
      _loadAbsencesRetardsData();
      return;
    }

    try {
      final phoneNumber = await _authLocalDataSource.getPhoneNumber();
      if (phoneNumber != null && mounted) {
        // Determine parameters based on user profile
        final codeEtudiant = _etudiant?.codeEtudiant ?? _school!.codeUtilisateur;
        final codeUtilisateur = _etudiant != null ? _school!.codeUtilisateur : null;
       
        context.read<AbsencesRetardsCubit>().loadAbsencesRetardsFiltres(
          codeEtab: _school!.codeEtab,
          telephone: phoneNumber,
          startDate: _startDateFilter!,
          endDate: _endDateFilter!,
          codeEtudiant: codeEtudiant,
          codeUtilisateur: codeUtilisateur,
        );
      
    }
    } catch (e) {
      debugPrint('Error loading absences retards data: $e');
    }
  }





  /// Method to filter absences retards based on search query and date range
  void _filterAbsencesRetards() {
    final String query = _searchController.text.toLowerCase();
    setState(() {
      _filteredAbsencesRetards = _allAbsencesRetards.where((absenceRetard) {
        // Text search filter
        bool matchesText = absenceRetard.cours.toLowerCase().contains(query) ||
               absenceRetard.professeur.toLowerCase().contains(query) ||
               absenceRetard.dateEnregistrement.toLowerCase().contains(query) ||
               absenceRetard.classe.toLowerCase().contains(query) ||
               absenceRetard.type.toLowerCase().contains(query);

        // Date range filter
        bool matchesDateRange = true;
        if (_startDateFilter != null && _endDateFilter != null) {
          try {
            final absenceRetardDate = DateTime.parse(absenceRetard.dateEnregistrement);
            final startDate = date_utils.parseDate(_startDateFilter!);
            final endDate = date_utils.parseDate(_endDateFilter!);

              matchesDateRange = absenceRetardDate.isAfter(startDate.subtract(const Duration(days: 1))) &&
                               absenceRetardDate.isBefore(endDate.add(const Duration(days: 1)));
          } catch (e) {
            // If date parsing fails, include the item
            matchesDateRange = true;
          }
        }

        return matchesText && matchesDateRange;
      }).toList();
    });
  }

  /// Handle date filter change
  void _onDateFilterChanged(Map<String, String> dateRange) {
    setState(() {
      _startDateFilter = dateRange['startDate'];
      _endDateFilter = dateRange['endDate'];
    });
    _loadAbsencesRetardsDataWithDateFilter();
  }




  /// Clear date filter
  void _clearDateFilter() {
    setState(() {
      _startDateFilter = null;
      _endDateFilter = null;
    });
    _filterAbsencesRetards();
  }

  /// Method to toggle search bar visibility
  void _toggleSearchBarVisibility() {
    setState(() {
      _isSearchBarVisible = !_isSearchBarVisible;
      if (!_isSearchBarVisible) {
        _searchAnimationController.reverse();
        _searchController.clear();
        _startDateFilter = null;
        _endDateFilter = null;
        _filterAbsencesRetards();
      } else {
        _searchAnimationController.forward();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: CustomScrollView(
          slivers: [
          CustomAppBar(
            isSearchBarVisible: _isSearchBarVisible,
            pageSection: HeaderEnum.absences,
            title: "ABSENCES & RETARDS",
            onSearchTap: _toggleSearchBarVisibility,
            etablissementUtilisateur: widget.school,
            enfantDuTuteur: widget.etudiant,
          ),
          // Search bar
          AnimatedBuilder(
            animation: _searchAnimationController,
            builder: (context, child) {
              return SliverPersistentHeader(
                delegate: SearchBarSliver(
                  extentHeight: _searchAnimationController.value * 60.0,
                  searchController: _searchController,
                  onSearchChanged: (query) => _filterAbsencesRetards(),
                  onDateFilterChanged: _onDateFilterChanged,
                  onClearDateFilter: _clearDateFilter,
                  hasActiveFilter: _startDateFilter != null && _endDateFilter != null,
                  hintText: "Rechercher absences et retards...",
                ),
                pinned: true,
              );
            }
          ),
          // Content based on BLoC state
          BlocBuilder<AbsencesRetardsCubit, AbsencesRetardsState>(
            builder: (context, state) {
              debugPrint("state----->: $state");
              if (state is AbsencesRetardsLoading) {
                return SliverFillRemaining(
                  child: Center(
                    child: CustomSpinner(
                      size: 60.0,
                      strokeWidth: 5.0,
                    ),
                  ),
                );
              } else if (state is AbsencesRetardsError) {
                return SliverFillRemaining(
                  child: Center(
                    child: EmptyMessage(message: state.message),
                  ),
                );
              } else if (state is AbsencesRetardsLoaded) {
                _allAbsencesRetards = state.absencesRetards;
                debugPrint("allAbsencesRetards: $_allAbsencesRetards");
                if (_filteredAbsencesRetards.isEmpty && _searchController.text.isEmpty && _startDateFilter == null) {
                  _filteredAbsencesRetards = _allAbsencesRetards;
                }

                if (_filteredAbsencesRetards.isNotEmpty) {
                  return SliverList(
                    delegate: SliverChildBuilderDelegate(
                      (context, index) {
                        final absenceRetard = _filteredAbsencesRetards[index];
                        return AbsenceItem(absenceRetard: absenceRetard);
                      },
                      childCount: _filteredAbsencesRetards.length,
                    ),
                  );
                } else {
                  return SliverFillRemaining(
                    child: Center(
                      child: EmptyMessage(message: "Aucune absence ou retard trouvé"),
                    ),
                  );
                }
              } else {
                return SliverFillRemaining(
                  child: Center(
                    child: EmptyMessage(message: "Aucune absence ou retard trouvé"),
                  ),
                );
              }
            },
          ),
        ],
        ),
      );
  }
}
