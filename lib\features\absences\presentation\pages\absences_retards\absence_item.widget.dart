import 'package:Kairos/features/absences/domain/entities/absence_retard_entity.dart';
import 'package:flutter/material.dart';

class AbsenceItem extends StatelessWidget {
  final AbsenceRetardEntity absenceRetard;

  const AbsenceItem({
    super.key,
    required this.absenceRetard,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: Card(
        elevation: 2,
        margin: EdgeInsets.zero,
        color: Theme.of(context).scaffoldBackgroundColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(4.0),
        ),
        child: IntrinsicHeight(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Type indicator (R or A)
              Container(
                width: 60,
                color: absenceRetard.type == 'R' ? Colors.orange : Colors.red,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      absenceRetard.type,
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 32,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    if (absenceRetard.formattedDuration != null)
                      Text(
                        absenceRetard.formattedDuration!,
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                        ),
                      ),
                  ],
                ),
              ),
              // Content
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.all(12.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Flexible(
                            child: Text(
                              absenceRetard.type == 'R' ? 'Enregistré le ${absenceRetard.dateEnregistrement}' : 'Enregistrée le ${absenceRetard.dateEnregistrement}',
                              style: TextStyle(
                                fontSize: 14,
                              ),
                            ),
                          ),
                          Flexible(
                            fit: FlexFit.tight,
                            child: Text(
                              absenceRetard.justifie
                                ? 'Justifié le ${absenceRetard.dateJustification ?? absenceRetard.dateEnregistrement}'
                                : 'Non Justifiée',
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                fontSize: 14,
                                color: absenceRetard.justifie ? Colors.green : Colors.red,
                              ),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 8),
                      Text(
                        absenceRetard.cours,
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      SizedBox(height: 4),
                      Text(
                        absenceRetard.professeur,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                      if (absenceRetard.classe.isNotEmpty) ...[
                        SizedBox(height: 4),
                        Text(
                          'Classe: ${absenceRetard.classe}',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[500],
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}