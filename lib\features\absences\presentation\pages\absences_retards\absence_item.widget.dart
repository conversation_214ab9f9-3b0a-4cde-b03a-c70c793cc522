import 'package:Kairos/features/absences/domain/entities/absence_retard_entity.dart';
import 'package:flutter/material.dart';

class AbsenceItem extends StatelessWidget {
  final AbsenceRetardEntity absenceRetard;

  const AbsenceItem({
    super.key,
    required this.absenceRetard,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 6.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.0),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.2),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: IntrinsicHeight(
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Left colored indicator
            Container(
              width: 6,
              decoration: BoxDecoration(
                color: absenceRetard.type == 'R' ? Colors.orange : Colors.red,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(8.0),
                  bottomLeft: Radius.circular(8.0),
                ),
              ),
            ),
            // Main content
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header row with type badge and date
                    Row(
                      children: [
                        // Type badge
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
                          decoration: BoxDecoration(
                            color: absenceRetard.type == 'R' ? Color(0xFFf87100) : Color(0xFFb61300),
                            borderRadius: BorderRadius.circular(12.0),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                absenceRetard.type == 'R' ? 'RETARD' : 'ABSENCE',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              if (absenceRetard.formattedDuration != null) ...[
                                const SizedBox(width: 4),
                                Text(
                                  absenceRetard.formattedDuration!,
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 11,
                                  ),
                                ),
                              ],
                            ],
                          ),
                        ),
                        const Spacer(),
                        // Date
                        Text(
                          absenceRetard.dateEnregistrement,
                          style: TextStyle(
                            fontSize: 13,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    // Course name
                    Text(
                      absenceRetard.cours,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 6),
                    // Teacher name
                    Text(
                      absenceRetard.professeur,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[700],
                      ),
                    ),
                    if (absenceRetard.classe.isNotEmpty) ...[
                      const SizedBox(height: 4),
                      Text(
                        'Classe: ${absenceRetard.classe}',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[500],
                        ),
                      ),
                    ],
                    const SizedBox(height: 12),
                    // Justification status
                    Row(
                      children: [
                        Icon(
                          absenceRetard.justifie ? Icons.check_circle : Icons.cancel,
                          size: 16,
                          color: absenceRetard.justifie ? Colors.green : Colors.red,
                        ),
                        const SizedBox(width: 6),
                        Text(
                          absenceRetard.justifie
                            ? 'Justifié${absenceRetard.dateJustification != null ? ' le ${absenceRetard.dateJustification}' : ''}'
                            : 'Non justifié',
                          style: TextStyle(
                            fontSize: 13,
                            color: absenceRetard.justifie ? Colors.green : Colors.red,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}