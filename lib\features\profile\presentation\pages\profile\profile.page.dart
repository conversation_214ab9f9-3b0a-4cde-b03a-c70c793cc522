import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:Kairos/core/widgets/indicators/custom_spinner.dart';
import 'package:Kairos/core/widgets/common/snackbar_widget.dart';
import 'package:Kairos/core/services/device_info_service.dart';
import 'package:Kairos/features/authentication/data/models/deconnexion_request.dart';
import '../../../domain/entities/profile_entity.dart';
import '../../bloc/profile_cubit.dart';
import '../../bloc/profile_state.dart';
import 'package:Kairos/features/schools/domain/entities/etablissement_utilisateur.dart';

class ProfilePage extends StatefulWidget {
  const ProfilePage({super.key, this.etablissementUser});

  final EtablissementUtilisateur? etablissementUser;

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  @override
  void initState() {
    super.initState();
    // Load profile data when page initializes, passing the codeUtilisateur
    if (widget.etablissementUser != null) {
      context.read<ProfileCubit>().loadProfileData(widget.etablissementUser!.codeUtilisateur);
    }
  }

  void _editProfileImage() {
    debugPrint('Edit profile image button tapped');
  }

  void _goToListSchools(){
    debugPrint("User wants to his list of schools");
    Navigator.popUntil(context, ModalRoute.withName('/liste_etablissement'));
  }

  // Function to show the account deletion confirmation alert
  void _showDeleteConfirmationAlert(ProfileEntity profile) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: Colors.white,
          title: const Text("CONFIRMATION DE SUPPRESSION", textAlign: TextAlign.center, style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold)),
          insetPadding: const EdgeInsets.symmetric(horizontal: 10),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(
                Icons.warning_amber_outlined,
                color: Colors.red,
                size: 50,
              ),
              const Text(
                "Êtes-vous sûr de vouloir supprimer votre compte ? Cette action est irréversible.",
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Colors.black,
                ),
              ),
            ],
          ),
          actionsAlignment: MainAxisAlignment.spaceBetween, // Align buttons to start and end
          actionsPadding: const EdgeInsets.all(15.0),
          actions: [
            // Left-aligned "SUPPRIMER" button
            FilledButton(
              style: ButtonStyle(
                foregroundColor: WidgetStateProperty.all(Colors.white),
                minimumSize: WidgetStateProperty.all(const Size(150, 50)),
                backgroundColor: WidgetStateProperty.all(Colors.red),
              ),
              onPressed: () {
                _logout(profile); // Execute the logout function
                Navigator.of(context).pop(); // Dismiss the alert
              },
              child: const Text('SUPPRIMER'),
            ),
            // Right-aligned "ANNULER" button
            FilledButton(
              style: ButtonStyle(
                backgroundColor: WidgetStateProperty.all(Theme.of(context).primaryColor),
                minimumSize: WidgetStateProperty.all(const Size(150, 50)), // Ensure consistent size
              ),
              onPressed: () {
                Navigator.of(context).pop(); // Dismiss the alert
              },
              child: const Text('ANNULER'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _logout(ProfileEntity profile) async {
    try {
      // Get device info
      final deviceInfo = DeviceInfoService.deviceInfo;

      // Create deconnexion request
      final request = DeconnexionRequest(
        numeroTelephone: profile.phoneNumber,
        marqueTelephone: deviceInfo.marqueTelephone,
        modelTelephone: deviceInfo.modelTelephone,
        imeiTelephone: deviceInfo.imeiTelephone,
        numeroSerie: deviceInfo.numeroSerie,
        codeUtilisateur: profile.codeUtilisateur,
        codeEtab: profile.schoolCode,
      );

    debugPrint('ProfilePage: Logout request: ${request.toJson()}');
      // Call logout
      context.read<ProfileCubit>().logout(request);
    } catch (e) {
      debugPrint('Error creating logout request: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<ProfileCubit, ProfileState>(
      listener: (context, state) {
        if (state is LogoutSuccess) {
          ScaffoldMessenger.of(context).showSnackBar(
            CustomSnackbar(
              message: "Votre demande de suppression de votre espace a été enregistrée avec succès",
            ).getSnackBar(),
          );
          Navigator.pushReplacementNamed(context, '/liste_etablissement');
        } else if (state is ProfileError) {
          ScaffoldMessenger.of(context).showSnackBar(
            CustomSnackbar(message: state.message).getSnackBar(),
          );
        }
      },
      child: Scaffold(
        backgroundColor: Colors.white,
        body: Column(
          children: [
            // Header section with curved bottom
            Container(
              height: 194,
              width: double.infinity,
              decoration: const BoxDecoration(
                color: Colors.black,
                borderRadius: BorderRadius.only(
                  bottomRight: Radius.circular(71),
                ),
              ),
              child: Stack(
                children: [
                  // Back button
                  Positioned(
                    left: 36,
                    top: 36,
                    child: GestureDetector(
                      onTap: () => Navigator.pop(context),
                      child: Container(
                        width: 23,
                        height: 36,
                        decoration: const BoxDecoration(
                          image: DecorationImage(
                            image: AssetImage('assets/images/back_arrow.png'), // You'll need to add this asset
                            fit: BoxFit.contain,
                          ),
                        ),
                      ),
                    ),
                  ),
                  // Title
                  Positioned(
                    left: 100,
                    top: 43,
                    child: const Text(
                      'Profil',
                      style: TextStyle(
                        fontFamily: 'Inter',
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                        height: 1.21,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            // Main content
            Expanded(
              child: BlocBuilder<ProfileCubit, ProfileState>(
                builder: (context, state) {
                  if (state is ProfileLoading || state is LogoutLoading) {
                    return Center(
                      child: CustomSpinner(
                        size: 60.0,
                        strokeWidth: 5.0,
                      ),
                    );
                  } else if (state is ProfileNotFound) {
                    return Center(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Text(
                          "Vous n'avez pas encore activé votre espace, veuillez activer un établissement afin d'accéder à votre profil",
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontSize: 16,
                            color: Theme.of(context).primaryColor,
                          ),
                        ),
                      ),
                    );
                  } else if (state is ProfileLoaded) {
                    return _buildProfileContent(state.profile);
                  } else if (state is ProfileError) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            'Erreur: ${state.message}',
                            textAlign: TextAlign.center,
                            style: const TextStyle(color: Colors.red),
                          ),
                          const SizedBox(height: 16),
                          ElevatedButton(
                            onPressed: () => context.read<ProfileCubit>().loadProfileData(widget.etablissementUser!.codeUtilisateur),
                            child: const Text('Réessayer'),
                          ),
                        ],
                      ),
                    );
                  }
                  return const SizedBox.shrink();
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileContent(ProfileEntity profile) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ListTile(
          title: Text('Nom complet', style: TextStyle(fontSize: 8, color: Theme.of(context).primaryColor)),
          subtitle: Text(profile.fullName, style: TextStyle(fontSize: 14)),
          contentPadding: const EdgeInsets.symmetric(vertical: 2, horizontal: 16),
          dense: true,
        ),
        Divider(height: 2, indent: 12, endIndent: 12, color: Theme.of(context).colorScheme.secondary,),
        ListTile(
          title: Text('Type de profil', style: TextStyle(fontSize: 8, color: Theme.of(context).primaryColor)),
          subtitle: Text(profile.profil, style: TextStyle(fontSize: 14)),
          contentPadding: const EdgeInsets.symmetric(vertical: 2, horizontal: 16),
          dense: true,
        ),
        Divider(height: 2, indent: 12, endIndent: 12, color: Theme.of(context).colorScheme.secondary,),
        ListTile(
          title: Text('Identifiant', style: TextStyle(fontSize: 8, color: Theme.of(context).primaryColor)),
          subtitle: Text(profile.codeUtilisateur, style: TextStyle(fontSize: 14)),
          contentPadding: const EdgeInsets.symmetric(vertical: 2, horizontal: 16),
          dense: true,
        ),
        Divider(height: 2, indent: 12, endIndent: 12, color: Theme.of(context).colorScheme.secondary,),
        ListTile(
          title: Text('Établissement', style: TextStyle(fontSize: 8,  color: Theme.of(context).primaryColor)),
          subtitle: Text(profile.schoolName, style: TextStyle(fontSize: 14)),
          contentPadding: const EdgeInsets.symmetric(vertical: 2, horizontal: 16),
          dense: true,
        ),
        Divider(height: 2, indent: 12, endIndent: 12, color: Theme.of(context).colorScheme.secondary,),
        ListTile(
          title: Text('Numéro de téléphone', style: TextStyle(fontSize: 8, color: Theme.of(context).primaryColor),),
          subtitle: Text(profile.phoneNumber, style: TextStyle(fontSize: 14)),
          contentPadding: const EdgeInsets.symmetric(vertical: 2, horizontal: 16),
          dense: true,
        ),
        Divider(height: 2, indent: 12, endIndent: 12, color: Theme.of(context).colorScheme.secondary,),
        const SizedBox(height: 40.0),
        Center(child:
        FilledButton(onPressed: _goToListSchools,
        style: ButtonStyle(
          minimumSize: WidgetStateProperty.all(const Size(200, 50)),
          backgroundColor: WidgetStateProperty.all(Theme.of(context).primaryColor),
        ),
        child: const Text("VOIR LISTE ÉTABLISSEMENT")),
        ),
        const SizedBox(height: 7.0),
        Center(
          child: FilledButton(
            onPressed: () => _showDeleteConfirmationAlert(profile), // Call the new alert function
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              minimumSize: const Size(200, 50),
            ),
            child: const Text(
              'DÉCONNEXION ÉTABLISSEMENT',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ),

        const SizedBox(height: 20.0),
      ],
    );
  }
}
