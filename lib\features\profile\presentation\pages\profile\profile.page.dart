import 'dart:convert';
import 'package:Kairos/features/profile/data/profile_type.enum.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:Kairos/core/widgets/indicators/custom_spinner.dart';
import 'package:Kairos/core/widgets/common/snackbar_widget.dart';
import 'package:Kairos/core/services/device_info_service.dart';
import 'package:Kairos/features/authentication/data/models/deconnexion_request.dart';
import '../../../domain/entities/profile_entity.dart';
import '../../bloc/profile_cubit.dart';
import '../../bloc/profile_state.dart';
import 'package:Kairos/features/schools/domain/entities/etablissement_utilisateur.dart';

class ProfilePage extends StatefulWidget {
  const ProfilePage({super.key, this.etablissementUser});

  final EtablissementUtilisateur? etablissementUser;

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  @override
  void initState() {
    super.initState();
    // Load profile data when page initializes, passing the codeUtilisateur
    if (widget.etablissementUser != null) {
      context.read<ProfileCubit>().loadProfileData(widget.etablissementUser!.codeUtilisateur);
    }
  }

  void _goToListSchools(){
    debugPrint("User wants to his list of schools");
    Navigator.popUntil(context, ModalRoute.withName('/liste_etablissement'));
  }

  // Function to show the account deletion confirmation alert
  void _showDeleteConfirmationAlert(ProfileEntity profile) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: Colors.white,
          title: const Text("CONFIRMATION DE SUPPRESSION", textAlign: TextAlign.center, style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold)),
          insetPadding: const EdgeInsets.symmetric(horizontal: 10),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(
                Icons.warning_amber_outlined,
                color: Colors.red,
                size: 50,
              ),
              const Text(
                "Êtes-vous sûr de vouloir supprimer votre compte ? Cette action est irréversible.",
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Colors.black,
                ),
              ),
            ],
          ),
          actionsAlignment: MainAxisAlignment.spaceBetween, // Align buttons to start and end
          actionsPadding: const EdgeInsets.all(15.0),
          actions: [
            // Left-aligned "SUPPRIMER" button
            FilledButton(
              style: ButtonStyle(
                foregroundColor: WidgetStateProperty.all(Colors.white),
                minimumSize: WidgetStateProperty.all(const Size(150, 50)),
                backgroundColor: WidgetStateProperty.all(Colors.red),
              ),
              onPressed: () {
                _logout(profile); // Execute the logout function
                Navigator.of(context).pop(); // Dismiss the alert
              },
              child: const Text('SUPPRIMER'),
            ),
            // Right-aligned "ANNULER" button
            FilledButton(
              style: ButtonStyle(
                backgroundColor: WidgetStateProperty.all(Theme.of(context).primaryColor),
                minimumSize: WidgetStateProperty.all(const Size(150, 50)), // Ensure consistent size
              ),
              onPressed: () {
                Navigator.of(context).pop(); // Dismiss the alert
              },
              child: const Text('ANNULER'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _logout(ProfileEntity profile) async {
    try {
      // Get device info
      final deviceInfo = DeviceInfoService.deviceInfo;

      // Create deconnexion request
      final request = DeconnexionRequest(
        numeroTelephone: profile.phoneNumber,
        marqueTelephone: deviceInfo.marqueTelephone,
        modelTelephone: deviceInfo.modelTelephone,
        imeiTelephone: deviceInfo.imeiTelephone,
        numeroSerie: deviceInfo.numeroSerie,
        codeUtilisateur: profile.codeUtilisateur,
        codeEtab: profile.schoolCode,
      );

    debugPrint('ProfilePage: Logout request: ${request.toJson()}');
      // Call logout
      context.read<ProfileCubit>().logout(request);
    } catch (e) {
      debugPrint('Error creating logout request: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    const double heroHeight = 200.0;

    return BlocListener<ProfileCubit, ProfileState>(
      listener: (context, state) {
        if (state is LogoutSuccess) {
          ScaffoldMessenger.of(context).showSnackBar(
            CustomSnackbar(
              message: "Votre demande de suppression de votre espace a été enregistrée avec succès",
            ).getSnackBar(),
          );
          Navigator.pushReplacementNamed(context, '/liste_etablissement');
        } else if (state is ProfileError) {
          ScaffoldMessenger.of(context).showSnackBar(
            CustomSnackbar(message: state.message).getSnackBar(),
          );
        }
      },
      child: Scaffold(
        extendBodyBehindAppBar: true,
        appBar: AppBar(
          title: const Text('Profile'),
          centerTitle: true,
          foregroundColor: Colors.white,
          backgroundColor: Colors.transparent,
          elevation: 0,
        ),
        body: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(
                height: heroHeight,
                child: Hero(
                  tag: "hero_profile",
                  transitionOnUserGestures: true,
                  child: Image.asset("assets/images/header_dashboard.png", width: MediaQuery.of(context).size.width, fit: BoxFit.cover,),
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(top: 0, left: 16, right: 16),
                child: BlocBuilder<ProfileCubit, ProfileState>(
                  builder: (context, state) {
                    if (state is ProfileLoading || state is LogoutLoading) {
                      return Center(
                        heightFactor: 4,
                        child: CustomSpinner(
                          size: 60.0,
                          strokeWidth: 5.0,
                        ),
                      );
                    } else if (state is ProfileNotFound) {
                      return Center(
                        heightFactor: 2,
                        child: Text(
                          "Vous n'avez pas encore activé votre espace, veuillez activer un établissement afin d'accéder à votre profil",
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontSize: 16,
                            color: Theme.of(context).primaryColor,
                          ),
                        ),
                      );
                    } else if (state is ProfileLoaded) {
                      return _buildProfileContent(state.profile);
                    } else if (state is ProfileError) {
                      return Center(
                        heightFactor: 4,
                        child: Column(
                          children: [
                            Text(
                              'Erreur: ${state.message}',
                              textAlign: TextAlign.center,
                              style: const TextStyle(color: Colors.red),
                            ),
                            const SizedBox(height: 16),
                            ElevatedButton(
                              onPressed: () => context.read<ProfileCubit>().loadProfileData(widget.etablissementUser!.codeUtilisateur),
                              child: const Text('Réessayer'),
                            ),
                          ],
                        ),
                      );
                    }
                    return const SizedBox.shrink();
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProfileContent(ProfileEntity profile) {
    return SingleChildScrollView(
      child: Stack(
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 83), // Space for profile card positioning
              // Main profile card matching Figma Rectangle 58
              Container(
                margin: const EdgeInsets.fromLTRB(59, 0, 51, 0),
                width: 302,
                decoration: BoxDecoration(
                  color: Colors.white,
                  border: Border.all(color: Colors.black.withValues(alpha: 0.2)),
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.25),
                      offset: const Offset(0, 4),
                      blurRadius: 4,
                      spreadRadius: 0,
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    const SizedBox(height: 106), // Space for profile image
                    // Update photo text
                    const Text(
                      'Mettre à jour la photo',
                      style: TextStyle(
                       
                        fontSize: 11,
                        fontWeight: FontWeight.w600,
                        color: Color(0xFF055A28),
                        height: 1.21,
                      ),
                    ),
                    const SizedBox(height: 13),
                    // User name
                    Text(
                      profile.fullName,
                      style: const TextStyle(
                       
                        fontSize: 12,
                        fontWeight: FontWeight.w800,
                        color: Colors.black,
                        height: 1.21,
                      ),
                    ),
                    const SizedBox(height: 13),
                    // Profile type
                    Text(
                      'Profil ${profile.profil == ProfileType.etudiant.code ? 'Étudiant' : profile.profil == ProfileType.tuteur.code ? 'Tuteur' : 'Parent'}',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w800,
                        color: Colors.black.withValues(alpha: 0.7),
                        height: 1.21,
                      ),
                    ),
                    const SizedBox(height: 71),
                  ],
                ),
              ),
              const SizedBox(height: 23),

              // School section
              _buildInfoSection('Etablissement', "${profile.schoolCode} - ${profile.schoolName}", hasIcon: true),
              const SizedBox(height: 23),

              // Phone section
              _buildInfoSection('Téléphone', profile.phoneNumber),
              const SizedBox(height: 23),

              // Voir les espaces section
              _buildActionSection(),
              const SizedBox(height: 127),

              // Logout button
              Container(
                width: 220,
                height: 46,
                decoration: BoxDecoration(
                  color: const Color(0xFF920000),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Center(
                  child: GestureDetector(
                    onTap: () => _showDeleteConfirmationAlert(profile),
                    child: Text(
                      'DÉCONNEXION ${profile.schoolName}',
                      style: const TextStyle(
                       
                        fontSize: 15,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                        height: 1.21,
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 20),

              // Delete account section
              _buildDeleteAccountSection(profile),
              const SizedBox(height: 20),
            ],
          ),
          // Profile image positioned on top of the card
          Positioned(
            left: 176,
            top: 107,
            child: _buildProfileImage(profile),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoSection(String title, String value, {bool hasIcon = false}) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 5),
      height: 70,
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Colors.black.withValues(alpha: 0.3)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.25),
            offset: const Offset(0, 4),
            blurRadius: 4,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title
            Text(
              title,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.black.withValues(alpha: 0.6),
                height: 1.21,
              ),
            ),
            const SizedBox(height: 2),
            // Value with optional icon
            Row(
              children: [
                if (hasIcon) ...[
                  Container(
                    width: 23,
                    height: 23,
                    decoration:
                        BoxDecoration(
                      image: DecorationImage(
                        image: MemoryImage(
                                        base64Decode(widget.etablissementUser!.logoEtablissement.replaceFirst(RegExp(r'^data:image\/[^;]+;base64,'), '')),
                                      ), // You'll need to add this asset
                        fit: BoxFit.contain,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                ],
                Expanded(
                  child: Text(
                    value,
                    style: TextStyle(
      
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: Colors.black.withValues(alpha: 0.6),
                      height: 1.21,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 5),
      height: 70,
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Colors.black.withValues(alpha: 0.3)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.25),
            offset: const Offset(0, 4),
            blurRadius: 4,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: _goToListSchools,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 7, vertical: 8),
            child: Row(
              children: [
                // Arrow icons
                Image.asset('assets/icons/arrow_right.png'), // You'll need to add this asset
                Image.asset('assets/icons/arrow_right.png'), // You'll need to add this asset
                
                const SizedBox(width: 16),
                // Text
                Text(
                  'Voir les espaces',
                  style: TextStyle(
    
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).primaryColor,
                    height: 1.21,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDeleteAccountSection(ProfileEntity profile) {
    return Center(
      child: GestureDetector(
        onTap: () => _showDeleteConfirmationAlert(profile),
        child: const Text(
          'Supprimer mon compte',
          style: TextStyle(
           
            fontSize: 13,
            fontWeight: FontWeight.w300,
            color: Color(0xFF920000),
            height: 1.21,
          ),
        ),
      ),
    );
  }

  Widget _buildProfileImage(ProfileEntity profile) {
    ImageProvider backgroundImage;
    if (profile.photo.isNotEmpty) {
      debugPrint("ProfilePage: Building profile image with photo: ${profile.photo}");
      try {
        backgroundImage = MemoryImage(
          base64Decode(profile.photo.replaceFirst(RegExp(r'^data:image\/[^;]+;base64,'), '')),
        );
      } catch (_) {
        backgroundImage = const AssetImage('assets/images/default_profile_image.jpg');
      }
    } else {
      debugPrint("ProfilePage: Building profile image with default image");
      backgroundImage = const AssetImage('assets/images/default_profile_image.jpg');
    }

    return Container(
      width: 69,
      height: 69,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(
          color: Colors.white,
          width: 3,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.25),
            offset: const Offset(0, 4),
            blurRadius: 4,
            spreadRadius: 0,
          ),
        ],
      ),
      child: CircleAvatar(
        radius: 32.5,
        backgroundImage: backgroundImage,
      ),
    );
  }
}
