import 'package:Kairos/core/di/injection_container.dart';
import 'package:Kairos/core/enums/header_enums.dart';
import 'package:Kairos/features/authentication/data/datasources/auth_local_datasource.dart';
import 'package:Kairos/features/schools/data/datasources/schools_local_datasource.dart';
import 'package:Kairos/features/schools/presentation/pages/dossier_selection/dossier_selection.page.dart';
import 'package:Kairos/features/student_records/domain/entities/enfant_tuteur_entity.dart';
import 'package:Kairos/features/schools/domain/entities/etablissement_utilisateur.dart'; // Import EtablissementUtilisateur
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'dart:convert';
import 'dart:typed_data';

import 'package:Kairos/features/profile/presentation/pages/profile/profile.page.dart';
import 'package:Kairos/features/schools/data/models/user_profile_model.dart'; // Import UserProfileModel

class HeroWidget extends StatefulWidget {
  const HeroWidget({
    super.key,
    this.pageSection = HeaderEnum.dashboard,
    this.enfantDuTuteur,
    this.etablissementUser, // Add the new optional parameter
  });

  final HeaderEnum pageSection;
  final EnfantTuteurEntity? enfantDuTuteur;
  final EtablissementUtilisateur? etablissementUser; // Declare the new parameter

  @override
  State<HeroWidget> createState() => _HeroWidgetState();
}

class _HeroWidgetState extends State<HeroWidget> {
  // Use late to initialize fullName and data source
  late final String? fullName; // Keep main user's full name for fallback
  late final SchoolsLocalDataSource _schoolsLocalDataSource;

  Uint8List? _profileImageBytes;
  bool _isLoadingImage = true;
  bool _hasImageError = false;
  String? _displayedFullName; // State variable for the displayed full name

  @override
  void initState() {
    super.initState();
    // Initialize data sources
    _schoolsLocalDataSource = sl<SchoolsLocalDataSource>();
    fullName = sl<AuthLocalDataSource>().getFullName(); // Get main user's full name

    // Load user profile data based on whether etablissementUser is provided
    if (widget.etablissementUser != null) {
      _loadSpecificUserProfileData(widget.etablissementUser!.codeUtilisateur);
    } else {
      // If no specific user is provided, hide the profile section immediately
      _isLoadingImage = false;
      _profileImageBytes = null;
      _displayedFullName = fullName;
    }
  }

  /// Load user profile data (including image and full name) for a specific
  /// user profile from SharedPreferences based on the provided codeUtilisateur.
  Future<void> _loadSpecificUserProfileData(String codeUtilisateur) async {
    if (!mounted) return; // Check if the widget is still mounted

    setState(() {
      _isLoadingImage = true;
      _hasImageError = false;
      _profileImageBytes = null;
      _displayedFullName = null;
    });

    try {
      debugPrint("HeroWidget: Loading specific user profile data for codeUtilisateur: $codeUtilisateur");
      final UserProfileModel? userProfile = await _schoolsLocalDataSource.getUserProfile(codeUtilisateur);

      if (userProfile != null) {
        // Update state with loaded data
        if (mounted) {
          setState(() {
            _displayedFullName = userProfile.utilisateur.nomComplet;
            if (userProfile.photo.isNotEmpty) {
              // Decode the base64 string
              debugPrint("HeroWidget: Decoding profile image for codeUtilisateur: ${userProfile.photo}");
              try {
                _profileImageBytes = base64Decode(userProfile.photo);
              } catch (e) {
                debugPrint('Error decoding profile image for $codeUtilisateur: $e');
                _hasImageError = true;
                _profileImageBytes = null; // Use default if decoding fails
              }
            } else {
              _profileImageBytes = null; // Use default if photo is empty
            }
            _isLoadingImage = false;
          });
        }
      } else {
        // Profile not found
        debugPrint("HeroWidget: User profile not found for codeUtilisateur: $codeUtilisateur");
        if (mounted) {
          setState(() {
            _isLoadingImage = false;
            _profileImageBytes = null; // Use default
            _displayedFullName = fullName; // Indicate profile not found
          });
        }
      }
    } catch (e) {
      // Error loading profile
      debugPrint('Error loading specific user profile for $codeUtilisateur: $e');
      if (mounted) {
        setState(() {
          _isLoadingImage = false;
          _hasImageError = true;
          _profileImageBytes = null; // Use default
          _displayedFullName = "Error Loading Profile"; // Indicate error
        }
        );
      }
    }
  }

  /// Build the profile avatar widget
  Widget _buildProfileAvatar() {
    if (_isLoadingImage) {
      return CircleAvatar(
        radius: 33,
        backgroundColor: Colors.grey[300],
        child: const SizedBox(
          width: 16,
          height: 16,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
          ),
        ),
      );
    }

    if (_profileImageBytes != null && !_hasImageError) {
      return CircleAvatar(
        radius: 33,
        backgroundImage: MemoryImage(_profileImageBytes!),
        onBackgroundImageError: (exception, stackTrace) {
          debugPrint('Error displaying profile image: $exception');
          if (mounted) {
            setState(() {
              _hasImageError = true;
            });
          }
        },
      );
    }

    // Default fallback image
    return CircleAvatar(
      radius: 33,
      backgroundImage: const AssetImage("assets/images/default_profile_image.jpg"),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Hero(
      tag: "hero_dashboard",
      transitionOnUserGestures: true,
      child: Stack(
        children: [
          Image.asset(
            widget.pageSection == HeaderEnum.dashboard ? "assets/images/header_dashboard.png" :
            widget.pageSection == HeaderEnum.notes ? "assets/images/header_notes.png" :
            widget.pageSection == HeaderEnum.absences ? "assets/images/header_absences.png" :
            widget.pageSection == HeaderEnum.cahierDeTexte ? "assets/images/header_cahier_texte.png" :
            widget.pageSection == HeaderEnum.planning ? "assets/images/header_planning.png" :
            widget.pageSection == HeaderEnum.dossiers ? "assets/images/header_dossiers.png" :
            widget.pageSection == HeaderEnum.finances ? "assets/images/header_finances.png" :
            "assets/images/header_dashboard.png",
            height: 189,
            width: MediaQuery.of(context).size.width,
            fit: BoxFit.cover,
          ),
          Positioned(
            top: 60,
            left: 20,
            width: MediaQuery.of(context).size.width - 40,
            child: Column(
                    children: [
                      // Display profile section only if etablissementUser is provided
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          mainAxisSize: MainAxisSize.max,
                          children: [
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start, // Align text to start
                              mainAxisAlignment: MainAxisAlignment.start,
                              mainAxisSize: MainAxisSize.max,
                              children: [
                                SvgPicture.asset(
                                  widget.pageSection == HeaderEnum.dashboard
                                      ? "assets/images/logo_kairos.svg"
                                      : "assets/images/logo_kairos_blanc.svg",
                                ),
                                SizedBox(height: 10),
                                // Display the loaded full name
                                Text(
                                  _displayedFullName ?? "",
                                  textAlign: TextAlign.left,
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.white,
                                  ),
                                ),
                              ],
                            ),
                            // Display the loaded profile avatar
                           if(widget.etablissementUser != null)
                           GestureDetector(
                              onTap: () {
                                Navigator.of(context).push(
                                  MaterialPageRoute(
                                    builder: (context) => ProfilePage(etablissementUser: widget.etablissementUser!,),
                                  ),
                                );
                              },
                              child:_buildProfileAvatar(),
                           ),
                           ],
                        ),
                      // The existing enfantDuTuteur section remains
                      if(widget.enfantDuTuteur != null)
                        Divider(color: Colors.white, thickness: 1, height: 10, indent: 10, endIndent: 10,),
                      if(widget.enfantDuTuteur != null)
                        ListTile(
                          leading: CircleAvatar(
                            radius: 20,
                            backgroundColor: Colors.grey[300],
                            backgroundImage: widget.enfantDuTuteur!.photo.isNotEmpty && widget.enfantDuTuteur!.photo != "null"
                                ? MemoryImage(base64Decode(widget.enfantDuTuteur!.photo))
                                : null,
                            child: widget.enfantDuTuteur!.photo.isEmpty
                                ? const Icon(Icons.person, color: Colors.grey)
                                : null,
                          ),
                          title: Text(
                            '${widget.enfantDuTuteur!.codeEtudiant} - ${widget.enfantDuTuteur!.prenom} ${widget.enfantDuTuteur!.nom}',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                            ),
                          ),
                          trailing: IconButton(
                            icon: Image.asset('assets/icons/switch_account.png', width: 20, height: 20,),
                            onPressed: () {
                              Navigator.popUntil(
                                          context,
                                          ModalRoute.withName('/dossier_selection'), // Replace with the name of your route                                          
                                        );
                            },
                          ),
                        ),
                    ],
                  ),
            ),
        ],
          ),
    );
  }
}