import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:Kairos/features/absences/presentation/pages/absences_retards/absences_retards.page.dart';
import 'package:Kairos/features/course_log/presentation/pages/cahier_texte/cahier_texte.page.dart';
import 'package:Kairos/features/student_records/presentation/pages/dossiers_etudiant/dossiers.page.dart';
import 'package:Kairos/features/schedule/presentation/pages/emploi_du_temps/emploi_du_temps.page.dart';
import 'package:Kairos/features/finances/presentation/pages/finances/finances.page.dart';
// import 'package:Kairos/features/schools/presentation/pages/liste_etablissements/liste_etablissement_widgets/alert.widget.dart';
import 'package:Kairos/features/grades/presentation/pages/notes/notes.page.dart';
import 'package:Kairos/core/widgets/common/snackbar_widget.dart';
import 'package:Kairos/core/constants/dashboard_strings.dart';
import 'package:Kairos/features/dashboard/data/dashboard_item_type.enum.dart';
import 'package:Kairos/features/schools/domain/entities/etablissement_utilisateur.dart';
import 'package:Kairos/features/student_records/domain/entities/enfant_tuteur_entity.dart';
import 'package:Kairos/features/grades/presentation/bloc/notes_cubit.dart';
import 'package:Kairos/features/finances/presentation/bloc/finances_cubit.dart';
import 'package:Kairos/core/di/injection_container.dart';

// enum DashboardItemType moved to data/dashboard_item_type.enum.dart

class DashboardItem extends StatelessWidget{
  final dynamic title;
  final dynamic subtitle;
  final dynamic iconName;
  final DashboardItemType? itemType;
  final EtablissementUtilisateur? school;
  final EnfantTuteurEntity? etudiant;

  const DashboardItem({
    super.key,
    required this.title,
    this.subtitle,
    required this.iconName,
    this.itemType,
    this.school,
    this.etudiant,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      shape: RoundedRectangleBorder(
        side: const BorderSide(color: Colors.grey, width: 1),
        borderRadius: BorderRadius.circular(2),
      ),
      color: Theme.of(context).scaffoldBackgroundColor,
      shadowColor: Colors.black,
      child: ListTile(
        dense: true,
        title: ListTile(
            dense: true,
            title: title is Widget ? DefaultTextStyle.merge(style: const TextStyle(fontSize: 12), child: title) : Text(title, style: const TextStyle(fontSize: 12)),
            subtitle: subtitle != null ? Text(subtitle, style: const TextStyle(fontSize: 11)) : null,
          ),
        leading: SvgPicture.asset("assets/icons/$iconName", width: 20, height: 20,),
        trailing: Icon(Icons.arrow_forward_ios, size: 10, weight: 900,),
        onTap: () {
          if (itemType != null) {
            switch(itemType!) {
              case DashboardItemType.notes:
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => BlocProvider(
                      create: (context) => sl<NotesCubit>(),
                      child: NotesPage(school: school, etudiant: etudiant),
                    ),
                    settings: RouteSettings(
                      arguments: {
                        'school': school,
                        'etudiant': etudiant,
                      },
                    ),
                  ),
                );
                break;
              case DashboardItemType.finances:
                if (school != null) {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => BlocProvider(
                        create: (context) => sl<FinancesCubit>(),
                        child: FinancesPage(school: school!, etudiant: etudiant),
                      ),
                    ),
                  );
                } else {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text("Erreur: Données de l'école non disponibles")),
                  );
                }
                break;
              case DashboardItemType.absences:
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => AbsencesRetardsPage()),
                );
                break;
              case DashboardItemType.dossiers:
                Navigator.push(context,
                MaterialPageRoute(builder: (context) => DossiersPage()));
                break;
              case DashboardItemType.cahierTexte:
                 Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => CahierTextePage()),
                );
                break;
              case DashboardItemType.planning:
                 Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => EmploiDuTempsPage()),
                );
                break;
              case DashboardItemType.ressources:
                 ScaffoldMessenger.of(context).showSnackBar(
                CustomSnackbar(message: "Fonctionnalité en cours de développement").getSnackBar()
              );
                break;
            }
          } else {
            // Fallback to old logic for backward compatibility
            switch(title.toString()) {
              case DashboardStrings.notesTitle:
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => BlocProvider(
                      create: (context) => sl<NotesCubit>(),
                      child: NotesPage(school: school, etudiant: etudiant),
                    ),
                    settings: RouteSettings(
                      arguments: {
                        'school': school,
                        'etudiant': etudiant,
                      },
                    ),
                  ),
                );
                break;
              case DashboardStrings.financesTitle:
                if (school != null) {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => BlocProvider(
                        create: (context) => sl<FinancesCubit>(),
                        child: FinancesPage(school: school!, etudiant: etudiant),
                      ),
                    ),
                  );
                } else {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text("Erreur: Données de l'école non disponibles")),
                  );
                }
                break;
              case DashboardStrings.absencesTitle:
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => AbsencesRetardsPage()),
                );
                break;
              case DashboardStrings.dossiersTitle:
                Navigator.push(context,
                MaterialPageRoute(builder: (context) => DossiersPage()));
                break;
              case DashboardStrings.cahierTitle:
                 Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => CahierTextePage()),
                );
                break;
              case DashboardStrings.planningTitle:
                 Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => EmploiDuTempsPage()),
                );
                break;
              default :
                 ScaffoldMessenger.of(context).showSnackBar(
                CustomSnackbar(message: "Fonctionnalité en cours de développement").getSnackBar()
              );
                break;
            }
          }
        },
      ),
    );
  }
}
