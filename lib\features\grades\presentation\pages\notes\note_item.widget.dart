import 'package:Kairos/features/grades/domain/entities/notes_evaluation_entity.dart';
import 'package:flutter/material.dart';

class NoteItem extends StatelessWidget {
  final NotesEvaluationEntity notesEvaluation;

  const NoteItem({
    super.key,
    required this.notesEvaluation,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 7.0, vertical: 2.0),
      child: Card(
        color: Theme.of(context).scaffoldBackgroundColor,
        margin: EdgeInsets.all(0.0),
        elevation: 2,
        child: IntrinsicHeight(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Left section with type, date, course and teacher
              Flexible(
                flex: 3,
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 10.0, vertical: 10.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            "${notesEvaluation.typeDevoir.toUpperCase()} · ",
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 14,
                            ),
                          ),
                          Text(
                            _formatDate(notesEvaluation.dateDevoir),
                            style: TextStyle(
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 8),
                      Text(
                        notesEvaluation.cours,
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      SizedBox(height: 4),
                      Text(
                        "Avec ${notesEvaluation.professeur}",
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[700],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              // Right section with semester and grade
              Flexible(
                flex: 2,
                child: Row(
                  children: [
                    // Semester section
                    Expanded(
                      child: Container(
                        padding: EdgeInsets.all(8.0),
                        color: Colors.grey[200],
                        child: Center(
                          child: Text(
                            "${notesEvaluation.classe} | ${notesEvaluation.semestre.toUpperCase()}",
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                    ),
                    // Grade section
                    Container(
                      width: 80,
                      color: notesEvaluation.note >= 10 ? Colors.green : Colors.red,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            notesEvaluation.note.toString(),
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            "Moy. Classe",
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 10,
                            ),
                          ),
                          Text(
                            notesEvaluation.moyenneClasse.toString(),
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Format date from API format (YYYY-MM-DD) to display format (DD/MM/YYYY)
  String _formatDate(String apiDate) {
    try {
      final parts = apiDate.split('-');
      if (parts.length == 3) {
        return '${parts[2]}/${parts[1]}/${parts[0]}';
      }
      return apiDate;
    } catch (e) {
      return apiDate;
    }
  }
}
